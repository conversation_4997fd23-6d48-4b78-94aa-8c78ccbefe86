version: "3.8"

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
    image: app
    container_name: app
    restart: unless-stopped
    env_file: .env
    ports:
      - "$PORT:$PORT"
    depends_on:
      - mongodb

  mongodb:
    image: mongo:6.0
    container_name: mongodb
    restart: unless-stopped
    env_file: .env
    environment:
      - MONGO_INITDB_ROOT_USERNAME=$DB_USER
      - MONGO_INITDB_ROOT_PASSWORD=$DB_PASS
    ports:
      - "$DB_PORT:$DB_PORT"
    volumes:
      - dbdata:/data/db

volumes:
  dbdata:
